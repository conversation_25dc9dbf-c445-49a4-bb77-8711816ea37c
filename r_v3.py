from airtest.core.api import *
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from loguru import logger
import json
import adbutils
import pymongo
# import openpyxl
from multiprocessing import Process
import multiprocessing
import traceback
import pandas as pd
import random
# import sys
# import clipboard

OUTPUT_FOLDER = 'result'

col = pymongo.MongoClient()['微信']['好友']
col.create_index('id')

# col_rz = pymongo.MongoClient()['鲸准']['融资信息']
# col_rz.create_index('id')
# col_gs = pymongo.MongoClient()['鲸准']['工商信息']
# col_gs.create_index('id')
# col_td = pymongo.MongoClient()['鲸准']['团队信息']
# col_td.create_index('id')

with open('配置文件.ini', 'r', encoding='utf-8')as f:
    readers = f.readlines()
    SLEEP = int(readers[0].split('==')[-1].split('##')[0].strip())
    DEVICE = readers[1].split('==')[-1].split('##')[0].strip()
    PACKAGE_NAME = readers[2].split('==')[-1].split('##')[0].strip()
    APK_PATH = readers[3].split('==')[-1].split('##')[0].strip()
    T_MIN = int(readers[4].split('==')[-1].split('##')[0].strip())
    T_MAX = int(readers[5].split('==')[-1].split('##')[0].strip())
    PHONE_NUM = int(readers[7].split('==')[-1].split('##')[0].strip())
    FILE = readers[6].split('==')[-1].split('##')[0].strip()


KEYS = []
try:
    with open('keys.txt', 'r', encoding='utf-8')as f:
        for line in f.readlines():
            KEYS.append(line.strip())
    # wb = openpyxl.load_workbook(FILE)
    # sheet = wb.active
    # for row in range(2, sheet.max_row+1):
    #     name = sheet[f'B{row}'].value
    #     if not name:
    #         continue
    #     if name in KEYS:
    #         continue
    #     elif col.find_one({'keyword': name}):
    #         logger.info(f'查询过了，跳过：{name}')
    #         continue
    #     else:
    #         KEYS.append(name)
        # logger.info(f'待查询关键词：{KEYS}')
except:
    input(f'请检查关键词文件:keys.txt')


KEYS_AVOIDANCE = []
try:
    with open('回避指定关键词好友.txt', 'r', encoding='utf-8')as f:
        for line in f.readlines():
            KEYS_AVOIDANCE.append(line.strip())
except:
    input(f'请检查关键词文件:回避指定关键词好友.txt')
KEYS_VERBAL = []
try:
    with open('话术.txt', 'r', encoding='utf-8')as f:
        for line in f.readlines():
            KEYS_VERBAL.append(line.strip())
except:
    input(f'请检查关键词文件:话术.txt')


class Controller(object):
    def __init__(self, device_uri, package_name, apk_path,  need_reinstall=False, need_restart=False, lock=''):
        self.device_uri = device_uri
        self.package_name = package_name
        self.apk_path = apk_path
        self.need_reintall = need_reinstall
        self.need_restart = need_restart
        self.lock = lock

    def connect_device(self):
        self.device = connect_device(self.device_uri)

    def install_app(self):
        if self.device.check_app(self.package_name) and not self.need_reintall:
            logger.info(f'app已安装')
            return
        logger.info(f'安装app')
        self.device.uninstall_app(self.package_name)
        self.device.install_app(self.apk_path)

    def start_app(self):
        if self.need_restart:
            logger.info(f'关闭app')
            self.device.stop_app(self.package_name)
            time.sleep(2)
        logger.info('启动app')
        self.device.start_app(self.package_name)
        time.sleep(5)

    def init_device(self):
        self.connect_device()
        self.poco = AndroidUiautomationPoco(self.device)
        self.window_width, self.window_height = self.poco.get_screen_size()
        # self.install_app()
        # self.start_app()

    def scroll_up(self):
        logger.info('向下滚动')
        self.device.swipe((self.window_width * 0.5, self.window_height * 0.8),
                          (self.window_width * 0.5, self.window_height * 0.3), duration=1)

    # 获取群成员列表
    def scrape_index(self):
        while True:
            try:
                elements = self.poco("com.tencent.mm:id/bgx").child("android.widget.LinearLayout")
                elements.wait_for_appearance()
                return elements
            except:
                logger.error(f' 获取群成员列表失败，等待')
                time.sleep(2)

    def save_(self, file_name, title):
        with open(file_name, 'a', encoding='utf-8') as f:
            f.write(title)
            f.write('\t\n')

    def run(self, key_data):
        device_name = key_data.split('--')[0].strip()
        keywords = key_data.split('--')[1:]
        for i in range(2):
            try:
                keyword = keywords[i].strip()
            except:
                return
            # 读取已经添加过的的群成员
            file_name = f'{device_name}-{keyword}.txt'
            names_file = []
            if os.path.exists(file_name):
                with open(file_name, 'r', encoding='utf-8')as f:
                    for line in f.readlines():
                        names_file.append(line.strip())
            logger.info(f'打开微信{i+1}')
            if i==0:
                # touch(Template(r"tpl1732871025931.png", record_pos=(0.005, 0.644), resolution=(1080, 2160)))
                touch(Template(f"微信1.png", record_pos=(0.005, 0.644), resolution=(1080, 2160)))
            else:
                # touch(Template(r"tpl1732871039687.png", record_pos=(0.194, 0.637), resolution=(1080, 2160)))
                touch(Template(f"微信2.png", record_pos=(0.194, 0.637), resolution=(1080, 2160)))
            time.sleep(2)

            logger.info(f'点击搜索')

            self.poco("com.tencent.mm:id/h5n").click()
            time.sleep(1)

            logger.info(f'输入群名称：{keyword}')
            try:
                self.poco(text="搜索").set_text(keyword)
            except:
                self.poco(type="android.widget.EditText").set_text(keyword)
            time.sleep(2)

            logger.info(f'点击群')
            self.poco("com.tencent.mm:id/odf").click()
            time.sleep(2)

            logger.info(f'点击更多信息')
            self.poco(desc="更多信息").click()
            time.sleep(2)

            logger.info(f'点击查看更多群成员')
            self.poco(text="查看更多群成员").click()
            time.sleep(2)
            names_file_ = self.scrape_list(names_file, file_name)
            if names_file_:
                names_file = names_file_

            logger.info('返回桌面')
            self.device.keyevent('HOME')
            logger.success(f'手机：{device_name}-群：{keyword}   处理完了')

    def scrape_detail(self, element, title):
        logger.info(f'点击:{title}')
        element.click()
        time.sleep(2)

        ele_ = self.poco(text="添加到通讯录")
        if ele_.wait(2).exists():
            logger.info("Element found!")
            logger.info(f'添加到通讯录:{title}')
            try:
                title1 = self.poco(textMatches="昵称:.*").get_text()
                title1 = title1.replace('昵称:', '').strip()
            except:
                title1 = title
            time.sleep(random.uniform(T_MIN, T_MAX))
            logger.info(f'点击设置备注和标签')
            self.poco(text="设置备注和标签").click()
            time.sleep(2)
            logger.info(f'设置备注:{title1}')
            self.poco(text="备注").sibling()[-1].set_text(title1)
            self.poco(text="保存").click()
            time.sleep(2)
            ele_.click()
            time.sleep(2)
        else:
            if self.poco(text="发消息").wait(1).exists():
                logger.info(f'已经添加过了，跳过：{title}')
            else:
                logger.error(f'没有找到添加到通讯录按钮，退出')
            # 返回一次
            self.device.keyevent('BACK')

            logger.info('返回群成员列表')
            time.sleep(2)
            return 2

        # 输入自定义内容
        content = random.choice(KEYS_VERBAL)
        logger.info(f'输入自定义内容:{content}')
        self.poco("com.tencent.mm:id/m9y").set_text(content)

        logger.info(f'发送:{title}')
        self.poco(text="发送").click()
        time.sleep(2)

        if self.poco(text="添加到通讯录").wait(1).exists():
            logger.info(f'{title}: 添加到通讯录正常')
            # 返回一次
            self.device.keyevent('BACK')
            logger.info('返回群成员列表')
            time.sleep(2)
            return 1
        if self.poco(text="操作过于频繁，请稍后再试。").wait(1).exists():
            logger.info(f'{title}: 操作过于频繁，请稍后再试。')
            self.poco(text="确定").click()
            time.sleep(2)
            # 返回两次
            self.device.keyevent('BACK')
            logger.info('返回群成员列表')
            time.sleep(2)
            self.device.keyevent('BACK')
            logger.info('返回群成员列表')
            time.sleep(2)
            return 3

        if self.poco(text="确定").wait(1).exists() or\
                self.poco(text="由于对方的隐私设置，你无法通过群聊将其添加至通讯录。").wait(1).exists() \
                or self.poco(text="对方账号违反了《微信个人账号使用规范》，无法添加朋友，可引导对方查看微信团队消息了解详情。").wait(1).exists() \
                or self.poco(text="对方拒绝接收你的消息").wait(1).exists():
            logger.info(f'{title}: 由于对方的隐私设置，你无法通过群聊将其添加至通讯录。')
            self.poco(text="确定").click()
            time.sleep(2)
            # 返回两次
            self.device.keyevent('BACK')
            logger.info('返回群成员列表')
            time.sleep(2)
            self.device.keyevent('BACK')
            logger.info('返回群成员列表')
            time.sleep(2)
            return 1
        else:
            logger.error(f'{title}: 添加到通讯录失败')
            return 1

    def scrape_list(self, names_file, file_name):
        num_today = 0
        record = 0
        page = 0
        is_end = 0
        names = []  # 上一页的群成员
        while True:
            # if is_end:
            #     logger.info('采集到最后了，退出')
            #     break
            elements = self.scrape_index()
            titles = []  # 本页的群成员
            i = 0
            for element in elements:
                i += 1
                # element_title = element.child("com.tencent.mm:id/mh9")
                element_title = element.child(type="android.widget.TextView")
                if not element_title.exists():
                    continue
                try:
                    title = element_title.attr('text')
                except:
                    continue
                titles.append(title)
                if title in names_file:
                    logger.info(f'已经采集过了，跳过：{title}')
                    continue
                is_avo = 0
                for key_ in KEYS_AVOIDANCE:
                    if key_ in title:
                        is_avo= 1
                        break
                if is_avo:
                    logger.info(f'回避指定关键词好友，跳过：{title}')
                    names_file.append(title)
                    self.save_(file_name, title)
                    continue
                if element.child("com.tencent.mm:id/mh_").exists():  # 企业微信标识
                    # title = element.child("com.tencent.mm:id/mh_").attr('text')
                    logger.info(f'企业微信，跳过：{title}')
                    names_file.append(title)
                    self.save_(file_name, title)
                    continue
                record += 1
                # 最后一页时不全部处理，否则只处理到0.7处
                if is_end==0:
                    _, element_y = element.get_position()
                    if element_y > 0.7:
                        logger.info('处理到0.7处，下滑翻页')
                        break
                logger.success(f'发现新的成员：第{record}条 - {title}')
                result = self.scrape_detail(element, title)
                if result==1:
                    names_file.append(title)
                    num_today += 1
                    self.save_(file_name, title)
                if result==2:
                    names_file.append(title)
                    self.save_(file_name, title)
                if result == 3:
                    return names_file

                if num_today >= PHONE_NUM:
                    logger.info(f'已达到今日上限:{PHONE_NUM}')
                    return names_file

                while True:
                    try:
                        self.poco("com.tencent.mm:id/bgx").child("android.widget.LinearLayout").wait_for_appearance(10)
                        break
                    except:
                        logger.error(f'返回群成员列表，重试')
                        time.sleep(2)
                    self.device.keyevent('BACK')
                    logger.info('返回群成员列表')
                    time.sleep(2)

            if is_end:
                logger.info('采集到最后了，退出')
                return names_file

            logger.info(f'上一页的群成员：{names}')
            logger.info(f'本页的群成员：{titles}')
            if page:
                if names == titles:
                    is_end = 1
                    logger.info('采集到最后了，退出')
                    # break

            self.scroll_up()
            time.sleep(2)
            names = titles
            page += 1


def run(l, device_uri, keyword):
    # while True:
    controller = Controller(device_uri=device_uri,
                            package_name=PACKAGE_NAME,
                            apk_path=APK_PATH,
                            need_reinstall=False,
                            need_restart=True,
                            lock=l)
    controller.init_device()
    # l.acquire()
    logger.info(f'待查询群：{keyword}')
    # keyword = KEYS.pop()
    # l.release()
    # if KEYS:
    try:
        controller.run(keyword)
        return
    except:
        traceback.print_exc()
        # continue
        # else:
        #     logger.info('没有关键词了，退出')
        #     return


def drop_db():
    col.drop()
    logger.info('清空数据库')


def save_(file, datas):
    # 保存为excel文件
    # if not os.path.exists(self.dir):
    #     os.mkdir(self.dir)
    current_time = time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime(time.time()))
    path = file + current_time + '.xlsx'
    # n = self.t + current_time + '.csv'
    # path = os.path.join(self.dir, n)
    df = pd.DataFrame(datas)
    # index_label = ['酒店名称', 'id', '开业时间', '房间数', '是否接待外宾', '经纬度', '地址', '城市', '地区']
    with pd.ExcelWriter(path, engine='xlsxwriter', options={'strings_to_urls': False}) as writer:
        df.to_excel(writer, index=False, encoding='utf-8-sig')
    # df.to_excel(writer, index=False, encoding='utf-8-sig', index_label=index_label)

    # df.drop(index=df.index)  # 将DataFrame  清空

    #  保存为csv
    # df.to_csv(path, index=False, encoding='utf-8-sig')


def import_():
    data = []
    items = col.find()
    for item in items:
        data.append(item)
    save_('联系方式', data)


if __name__ == '__main__':
    # c = input('请选择：\n1、采集联系方式\n2、导出\n3、清空数据库\n')
    c = '1'
    if c == '1':
        logger.info(f'开始采集')
        l = multiprocessing.Lock()

        # # 无线连接
        # device_name = '*************:5555'
        # device_uri = f'android://127.0.0.1:5037/{device_name}'
        # # device_uri = f'android://127.0.0.1:5037/*************:5555'
        # Process(target=run, args=(l, device_uri)).start()
        # logger.info(f'读取所有关键词：{KEYS}')
        # 自动运行多个有线连接的手机
        adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
        adbs = adb.device_list()
        device_names = []
        for device in adbs:
            device_name = device.serial
            logger.info(f'当前设备：{device_name}')
            device_names.append(device_name)
            for key_data in KEYS:
                name = key_data.split('--')[0].strip()
                key = key_data.split('--')[1:]
                if device_name == name:
                    logger.success(f'匹配到手机：{device_name} - 群：{key_data}')
                    device_uri = f'Android:///{device_name}'
                    Process(target=run, args=(l, device_uri, key_data)).start()
                    break
        if device_names ==[]:
            logger.info('没有手机连接，请手动检查')

    elif c == '2':
        logger.info(f'导出数据')
        import_()
    elif c == '3':
        drop_db()
    else:
        logger.info('输入错误')

